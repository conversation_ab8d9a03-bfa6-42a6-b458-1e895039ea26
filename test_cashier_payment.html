<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار أيقونة الدفع للكاشير</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@tabler/icons@latest/icons-sprite.svg">
    <style>
        .pay-button {
            background-color: #ffc107;
            border-color: #ffc107;
            color: #000;
        }
        .pay-button:hover {
            background-color: #ffca2c;
            border-color: #ffc720;
            color: #000;
        }
        .badge {
            font-size: 0.85rem;
            padding: 0.35em 0.6em;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h2 class="text-center mb-4">اختبار أيقونة الدفع للكاشير</h2>
        
        <div class="alert alert-info">
            <h5>📋 ملخص التحديثات:</h5>
            <ul>
                <li>✅ تم إضافة أيقونة الدفع للكاشير في شاشة POS Report</li>
                <li>✅ تم إضافة أيقونة الدفع للكاشير في شاشة POS Summary</li>
                <li>✅ تم تحديث دالة collectPayment للسماح للكاشير بتحصيل الدفع</li>
                <li>✅ تم تحديث دالة report لعرض الفواتير للكاشير بشكل صحيح</li>
            </ul>
        </div>

        <div class="card">
            <div class="card-header">
                <h5>مثال على عرض الفواتير للكاشير</h5>
            </div>
            <div class="card-body">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>العميل</th>
                            <th>حالة الدفع</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- فاتورة تم التحصيل -->
                        <tr>
                            <td>POS-001</td>
                            <td>عميل التوصيل 1</td>
                            <td>
                                <span class="badge bg-success text-white">تم التحصيل من مندوب التوصيل ✅</span>
                            </td>
                            <td>
                                <button class="btn btn-sm btn-success" title="طباعة الفاتورة الحرارية">
                                    <i class="ti ti-printer"></i>
                                </button>
                                <!-- لا يظهر زر الدفع لأنه تم التحصيل بالفعل -->
                            </td>
                        </tr>
                        
                        <!-- فاتورة جاري التحصيل - يظهر زر الدفع للكاشير -->
                        <tr>
                            <td>POS-002</td>
                            <td>عميل التوصيل 2</td>
                            <td>
                                <span class="badge bg-warning text-dark">جاري التحصيل 🚚</span>
                            </td>
                            <td>
                                <button class="btn btn-sm btn-success" title="طباعة الفاتورة الحرارية">
                                    <i class="ti ti-printer"></i>
                                </button>
                                <!-- زر الدفع يظهر فقط للكاشير لفواتير جاري التحصيل -->
                                <button class="btn btn-sm btn-warning ms-2 pay-button" 
                                        data-id="2" 
                                        title="تحصيل الدفع من مندوب التوصيل">
                                    💰
                                </button>
                            </td>
                        </tr>
                        
                        <!-- فاتورة نقد عادية -->
                        <tr>
                            <td>POS-003</td>
                            <td>عميل عادي</td>
                            <td>
                                <span class="badge bg-success text-white">نقد 💵</span>
                            </td>
                            <td>
                                <button class="btn btn-sm btn-success" title="طباعة الفاتورة الحرارية">
                                    <i class="ti ti-printer"></i>
                                </button>
                                <!-- لا يظهر زر الدفع للفواتير العادية -->
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="alert alert-success mt-4">
            <h5>🎯 كيفية عمل النظام:</h5>
            <ol>
                <li><strong>للكاشير:</strong> يظهر زر الدفع (💰) فقط لفواتير "جاري التحصيل"</li>
                <li><strong>لمندوب التوصيل:</strong> يظهر زر الدفع فقط لفواتيره الخاصة</li>
                <li><strong>للمستخدمين الآخرين:</strong> لا يظهر زر الدفع</li>
                <li><strong>عند النقر على زر الدفع:</strong> يتم تحديث حالة الفاتورة إلى "تم التحصيل"</li>
            </ol>
        </div>

        <div class="alert alert-warning">
            <h5>⚠️ ملاحظات مهمة:</h5>
            <ul>
                <li>يجب أن يكون هناك شفت مفتوح للكاشير لتحصيل الدفع</li>
                <li>زر الدفع يظهر فقط للفواتير التي لم يتم تحصيلها بعد</li>
                <li>يتم إنشاء سجل مالي عند تحصيل الدفع</li>
            </ul>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // محاكاة وظيفة زر الدفع
        document.querySelectorAll('.pay-button').forEach(button => {
            button.addEventListener('click', function() {
                const invoiceId = this.getAttribute('data-id');
                if (confirm('هل أنت متأكد من تحصيل هذه الفاتورة؟')) {
                    alert('تم تحصيل الفاتورة رقم ' + invoiceId + ' بنجاح!');
                    // في النظام الحقيقي، سيتم إرسال طلب AJAX هنا
                }
            });
        });
    </script>
</body>
</html>
